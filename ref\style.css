@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

*{
    margin: 0;
    padding: 0;
}

body{
    font-family: 'Montserrat', sans-serif;
}

body a{
    text-decoration: none;
}

.topWrapper{
    height: 70px;
    width: 100%;
    position: fixed;
    top: 0;
    background: white;
    z-index: 5!important;
}

.header{
    padding: 0px 30px;
    height: 70px;
}
.logo{
    width: 120px;
    padding: 0px 30px;
    cursor: pointer;
}

.logo img{
    float: left;
    height: 70px;
}

nav{
    width: 1140px;
    
    
}

.mainMenu{
    float: right;
    margin: auto;
    

}

.mainMenu li{
    list-style: none;
    float: left;
    position: relative;
    z-index: 5;
}

.mainMenu li a{
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    font-size: 15px;
    display: block;
    padding: 20px 40px;
    margin-top: 2px;
    font-weight: 500;
    color: black;
    
}

.mainMenu li:hover{
    text-decoration: underline;
}



.mainMenu li:hover ul{
    visibility: visible;
}

.subMenu{
    list-style: none;
    width: 990px;
    height: 500px;
    margin-left: -250px;
    position: absolute;
    background: white;
    visibility: hidden;
}

.subMenu li{
    float: left;
    text-decoration: none;
}

.subMenu li:hover{
    text-decoration: none;
}

.subMenu li a{
    padding: 5px 40px;
}

.subMenu li ul li{
    float: none;
}

.top-a{
    padding-bottom: 30px !important;
    cursor: default;
}



.subMenu li ul li a:hover{
    text-decoration: underline;
}


















.subMenu-1{
    list-style: none;
    width: 990px;
    height: 500px;
    margin-left: -366px;
    position: absolute;
    background: white;
    visibility: hidden;
}

.subMenu-1 li{
    float: left;
    text-decoration: none;
}

.subMenu-1 li:hover{
    text-decoration: none;
}

.subMenu-1 li a{
    padding: 5px 40px;
}

.subMenu-1 li ul li{
    float: none;
}

.top-a{
    padding-bottom: 30px !important;
    cursor: default;
}


.subMenu-1 li ul li a:hover{
    text-decoration: underline;
}












.subMenu-2{
    list-style: none;
    width: 990px;
    height: 500px;
    margin-left: -492px;
    position: absolute;
    background: white;
    visibility: hidden;
}

.subMenu-2 li{
    float: left;
    text-decoration: none;
}

.subMenu-2 li:hover{
    text-decoration: none;
}

.subMenu-2 li a{
    padding: 5px 40px;
}

.subMenu-2 li ul li{
    float: none;
}

.top-a{
    padding-bottom: 30px !important;
    cursor: default;
}


.subMenu-2 li ul li a:hover{
    text-decoration: underline;
}


.icon{
    width: 1920px;
    float: right;
    margin-top: -61px;
}


/* Slider Css */

.wrapper{
    width: 100%;
    overflow: hidden;
}

.slides-container{
    height: 950px;
    transition: 900ms cubic-bezier(0.48, 0.15, 0.18, 1);
    position: relative;
}

.slide-image{
    height: 100%;
    width: 100%;
    position: absolute;
}

.slide-image img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.next-btn,.prev-btn{
    padding: 16px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 32px;
    color: #eee;
    cursor: pointer;
    transition: 400ms;
}

.next-btn:hover,.prev-btn:hover{
    color: white;
}

.prev-btn{
    left: 0;
}

.next-btn{
    right: 0;
}
 /* Navigatin Dots*/

 .navigation-dots{
     display: flex;
     height: 32px;
     align-items: center;
     justify-content: center;
     margin: -55px 0;
     position: relative;
}

.single-dot{
    background-color: white;
    opacity: 0.4;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    margin: 0 8px;
    cursor: pointer;
    z-index: 100;
    transition: 400ms;
}

.single-dot.active{
    background: white;
    opacity: 100;
}





/* footer */

.footer a{
    color: #595755;
}

.footer::before{
    display: block;
    width: 100%;
    height: 135px;
    content: '';
    clear: both;
    background-color: #fff;
    margin-top: 80px;

}

.footmenu{
    padding: 65px 0 30px;
    background-color: #F4F3F1;
}

.footcont{
    display: block;
    max-width: 1280px;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    padding: 0 15px;
    
}

.footaddpan{
    min-height: 0;
    font-size: 13px;
    border-bottom: 1px solid #d7d4d1;
    padding: 15px 0;
    position: relative;
}

.footaddpan p a{
    display: inline-block;
    padding-right: 15px;
    font-weight: 400;
    text-transform: none;
}

.footaddpan::after{
    display: block;
    width: 100%;
    height: 1px;
    content: '';
    clear: both;
}

.vsi{
    top: 10px;
    width: 25%;
    min-width: 250px;
    transform: translateY(0);
    background-color: #F4F3F1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: row;
    position: absolute;
    right: 0;
}

.vsi a{
    display: inline-block;
    padding-right: 15px;
    font-weight: 400;
    text-transform: none;
    padding: 10px 0;
    padding-right: 10px;

}

.vsi-logo{
    background: url(img/trustmark.png) no-repeat center;
    display: inline-block;
    height: 70px;
    width: 70px;
}

.footlink{
    display: block;
    width: 100%;
    overflow: auto;
}
.footlink a{
    font-size: 12px;
    padding: 5px 0;
}

.footlink ul li{
    font-size: 16px;
    font-weight: 400;
}

.footlink ul li a{
    font-size: 13px;
    line-height: 1.35;
    padding: 8px 0;
    display: inherit;
    text-decoration: none;
    text-transform: none;
}

.footlink h4{
    font-size: 14px;
    font-weight: 400;
    text-transform: uppercase;
    color: #202020;
    padding: 10px 0;

}

.footlink a:hover{
    color: #d4ae33;
}

.accordion{
    position: relative;
    overflow: hidden;
}

.accordion-cat{
    width: 25%;
    display: block;
    box-sizing: border-box;
    float: left;
    padding-right: 10px;
}

.accordion-cat:last-of-type{
    float: right;
    padding-right: 0;
}

.accordion-cat.brand-cat{
    position: absolute;
    top: 150px;
    left: 50%;
}

.accordion-cat-title{
    cursor: auto;
    position: relative;
}

.accordion-cat-content{
    max-height: inherit;
    transition: all linear .3s;
    overflow: hidden;
    margin: 0;
}

.accordion::after{
    display: block;
    width: 100%;
    height: 1px;
    content: '';
    clear: both;
}

.footnewsletter p{
    font-size: 13px;
    line-height: 22px;
}

.footnewsletter a{
    text-decoration: underline !important;
}

.inputwrapper{
    margin: 20px 0;
}

.inputwrapper .icon-mail{
    height: 40px;
    width: 38px;
    position: absolute;
    background-position: center;
    background-size: 22px auto;
    z-index: 1;
    font-size: 21px;
    margin-top: 8px;
    margin-left: 8px;
    color: #eee;
}

.inputwrapper input[type=text]{
    width: 100%;
    height: 35px;
    border: none;
    padding-left: 35px;
    position: relative;
    border-bottom: 1px solid #5957553d;
    outline: none;
}


.checkbox-wrapper{
    margin: 22px 0;
    position: relative;
}

.checkbox-inside{
    line-height: 15px;
    height: 15px;
    min-height: 18px;
    font-size: 14px;

}
.checkbox-inside input{
    margin-right: 5px;
}

.submit-button{
    background-color: white;
    color: black !important;
    border: solid 2px black;
    max-width: 360px !important;
    width: 150px;
    display: inline-block;
    padding: 13px 20px;
    outline: 0;
    min-height: 40px;
    box-sizing: border-box;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    position: relative;
    cursor: pointer;
    font-weight: 600;
    letter-spacing: .35px;
    text-decoration: none !important;
    font-family: 'Montserrat', sans-serif;
}

.submit-button:hover{
    background-color: black;
    color: white !important;
}

.footsocial{
    display: block;
    float: left;
    padding: 74px 0 30px;
    width: 300px;
}

.footsocial ul{
    margin: 0 -8px;
    text-align: left;
    list-style: none;
}

.footsocial ul li{
    background: no-repeat center / 40px;
    padding: 0 17px 20px;
    width: 40px;
    height: 40px;
    position: relative;
    display: inline-block;
}

.footsocial ul li a{
    letter-spacing: normal;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    line-height: 110px;
    font-weight: 400;
    color: #595755;
    opacity: 1;
}

.footsocial ul li.facebook{
    background-image: url(img/facebook.png);
}
.footsocial ul li.youtube{
    background-image: url(img/youtube.png);
}
.footsocial ul li.instragram{
    background-image: url(img/instagram.png);
}
.footsocial ul li.pinterest{
    background-image: url(img/pinterest.svg);
}

.social-title{
    display: block;
    text-transform: none;
    font-size: 14px;
    line-height: 30px;
    color: #595755;
}

.fbb{
    background-color: #202020;
    color: #696763;
    padding: 15px 0 20px;
}


.jj p{
    padding-top: 5px;
}

.footcont,.footcr{
    font-size: 12px;
    line-height: 18px;
}











/* (search box)


.search-box{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,50%);
    height: 40px;
    border-radius: 40px;
    padding: 10px;
}

.search-btn{
    color: black;
    float: right;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;


}

.search-txt{
    border: none;
    background: none;
    outline: none;
    float: left;
    padding: 0;
    color: black;
    font-size: 16px;
    transition: 0.4s;
    line-height: 40px;
    width: 0px;
}

.search-box:hover > .search-txt{
    width: 240px;
    padding: 0 6px;
}



------------

.search{
    float: right;
    margin: auto;
}


.button{
    height: 70px;
    width: 65px;
    background-position: center 15px;
    background-size: 24px;
    font-size: 9px;
}



.button{
    color: black;
    height: 58px;
    width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    border: none;
    cursor: pointer;
    font-size: 7px;
    letter-spacing: 0.35px;
    padding: 28px 0px 0px;
    text-align: center;
    font-weight: 600;
    text-transform: uppercase;
    position: relative;
    z-index: 101;
    white-space: nowrap;
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHRpdGxlPnNlYXJjaDwvdGl0bGU+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBzdHlsZT0iZmlsbDojZmZmO29wYWNpdHk6MCIvPjxwYXRoIGQ9Ik0yMS44MSwyMC42NmwtNC41Mi00LjUxYTguNjMsOC42MywwLDEsMC0xLjA2LDEuMDZsNC41MSw0LjUxWk0zLjczLDEwLjczYTcsNywwLDEsMSw3LDdBNyw3LDAsMCwxLDMuNzMsMTAuNzNaIi8+PC9zdmc+) center 15px / 22px no-repeat;
}



*/

